#!/usr/bin/env node

/**
 * 小梅花AI智能客服 - 网络连接测试工具
 * 用于排查登录时出现的网络错误问题
 */

const axios = require('axios');
const dns = require('dns');
const { promisify } = require('util');
const net = require('net');

// 服务器配置
const SERVER_CONFIG = {
  main: 'https://xiaomeihuakefu.cn',
  backup: 'https://api.xiaomeihuakefu.cn',
  secure: 'https://secure.xiaomeihuakefu.cn',
  timeout: 8000,
  retries: 1
};

// 测试用的卡密（请替换为实际的测试卡密）
const TEST_LICENSE = 'XMHS-E2EFA8698EEC178FBE07';

// DNS解析测试
async function testDNS() {
  console.log('\n=== DNS解析测试 ===');
  const resolve = promisify(dns.resolve);
  
  const domains = [
    'xiaomeihuakefu.cn',
    'api.xiaomeihuakefu.cn',
    'secure.xiaomeihuakefu.cn'
  ];
  
  for (const domain of domains) {
    try {
      const addresses = await resolve(domain);
      console.log(`✅ ${domain} -> ${addresses.join(', ')}`);
    } catch (error) {
      console.log(`❌ ${domain} -> DNS解析失败: ${error.message}`);
    }
  }
}

// TCP连接测试
async function testTCPConnection(hostname, port = 443) {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    const timeout = 5000;
    
    socket.setTimeout(timeout);
    
    socket.on('connect', () => {
      socket.destroy();
      resolve({ success: true });
    });
    
    socket.on('timeout', () => {
      socket.destroy();
      resolve({ success: false, error: '连接超时' });
    });
    
    socket.on('error', (error) => {
      socket.destroy();
      resolve({ success: false, error: error.message });
    });
    
    socket.connect(port, hostname);
  });
}

// HTTP连接测试
async function testHTTPConnection() {
  console.log('\n=== HTTP连接测试 ===');
  
  for (const [name, url] of Object.entries(SERVER_CONFIG)) {
    if (name === 'timeout' || name === 'retries') continue;
    
    try {
      console.log(`测试 ${name}: ${url}`);
      
      // 测试TCP连接
      const hostname = new URL(url).hostname;
      const tcpResult = await testTCPConnection(hostname);
      
      if (!tcpResult.success) {
        console.log(`❌ TCP连接失败: ${tcpResult.error}`);
        continue;
      }
      
      console.log(`✅ TCP连接成功`);
      
      // 测试HTTP请求
      const response = await axios.get(url, {
        timeout: SERVER_CONFIG.timeout,
        validateStatus: () => true // 接受所有状态码
      });
      
      console.log(`✅ HTTP响应: ${response.status} ${response.statusText}`);
      
    } catch (error) {
      console.log(`❌ HTTP请求失败: ${error.message}`);
      
      if (error.code) {
        console.log(`   错误代码: ${error.code}`);
      }
      
      if (error.response) {
        console.log(`   响应状态: ${error.response.status}`);
      }
    }
  }
}

// API接口测试
async function testAPIEndpoints() {
  console.log('\n=== API接口测试 ===');
  
  const endpoints = [
    '/api/verify.php',
    '/api/status.php'
  ];
  
  for (const [serverName, baseUrl] of Object.entries(SERVER_CONFIG)) {
    if (serverName === 'timeout' || serverName === 'retries') continue;
    
    console.log(`\n测试服务器: ${serverName} (${baseUrl})`);
    
    for (const endpoint of endpoints) {
      try {
        const url = `${baseUrl}${endpoint}`;
        console.log(`  测试接口: ${endpoint}`);
        
        const response = await axios.post(url, 
          new URLSearchParams({
            key: TEST_LICENSE,
            test: '1'
          }).toString(),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'xiaomeihua-app/1.0.0'
            },
            timeout: SERVER_CONFIG.timeout,
            validateStatus: () => true
          }
        );
        
        console.log(`    ✅ 响应: ${response.status} ${response.statusText}`);
        
        if (response.data) {
          const dataStr = typeof response.data === 'string' 
            ? response.data.substring(0, 100) 
            : JSON.stringify(response.data).substring(0, 100);
          console.log(`    数据: ${dataStr}...`);
        }
        
      } catch (error) {
        console.log(`    ❌ 请求失败: ${error.message}`);
        
        if (error.code) {
          console.log(`    错误代码: ${error.code}`);
        }
      }
    }
  }
}

// 网络环境检测
async function testNetworkEnvironment() {
  console.log('\n=== 网络环境检测 ===');
  
  // 检测代理设置
  const proxyEnvs = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy'];
  let hasProxy = false;
  
  for (const env of proxyEnvs) {
    if (process.env[env]) {
      console.log(`🔍 检测到代理设置: ${env}=${process.env[env]}`);
      hasProxy = true;
    }
  }
  
  if (!hasProxy) {
    console.log('✅ 未检测到代理设置');
  }
  
  // 检测防火墙/安全软件
  console.log('\n🔍 常见网络问题检查:');
  console.log('1. 请检查防火墙是否阻止了应用程序的网络访问');
  console.log('2. 请检查杀毒软件是否阻止了网络连接');
  console.log('3. 请检查是否使用了VPN或代理服务');
  console.log('4. 请检查网络连接是否正常');
}

// 修复建议
function showFixSuggestions() {
  console.log('\n=== 修复建议 ===');
  console.log('如果出现网络连接问题，请尝试以下解决方案：');
  console.log('');
  console.log('1. 检查网络连接');
  console.log('   - 确保设备已连接到互联网');
  console.log('   - 尝试访问其他网站确认网络正常');
  console.log('');
  console.log('2. 检查防火墙设置');
  console.log('   - Windows: 控制面板 > 系统和安全 > Windows Defender 防火墙');
  console.log('   - macOS: 系统偏好设置 > 安全性与隐私 > 防火墙');
  console.log('   - 将小梅花AI应用添加到防火墙白名单');
  console.log('');
  console.log('3. 检查杀毒软件');
  console.log('   - 暂时禁用杀毒软件的网络保护功能');
  console.log('   - 将应用程序添加到杀毒软件的信任列表');
  console.log('');
  console.log('4. 检查代理设置');
  console.log('   - 如果使用代理，请确保代理设置正确');
  console.log('   - 尝试暂时禁用代理');
  console.log('');
  console.log('5. DNS设置');
  console.log('   - 尝试更换DNS服务器（如8.8.8.8或114.114.114.114）');
  console.log('   - 清除DNS缓存');
  console.log('');
  console.log('6. 重启网络');
  console.log('   - 重启路由器');
  console.log('   - 重启网络适配器');
}

// 主测试函数
async function runNetworkTest() {
  console.log('小梅花AI智能客服 - 网络连接测试工具');
  console.log('=====================================');
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`操作系统: ${process.platform} ${process.arch}`);
  
  try {
    await testDNS();
    await testHTTPConnection();
    await testAPIEndpoints();
    await testNetworkEnvironment();
    
    console.log('\n=== 测试完成 ===');
    console.log('✅ 网络连接测试已完成');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
  }
  
  showFixSuggestions();
}

// 运行测试
if (require.main === module) {
  runNetworkTest().catch(console.error);
}

module.exports = {
  runNetworkTest,
  testDNS,
  testHTTPConnection,
  testAPIEndpoints
};
