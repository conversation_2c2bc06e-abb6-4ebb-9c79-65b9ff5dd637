#!/usr/bin/env node

/**
 * 小梅花AI智能客服 - 登录问题诊断工具
 * 专门用于排查登录时出现的网络错误
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const crypto = require('crypto');

// 简单的UUID生成函数
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 服务器配置（与应用程序保持一致）
const SERVER_CONFIG = {
  main: 'https://xiaomeihuakefu.cn',
  backup: 'https://api.xiaomeihuakefu.cn',
  secure: 'https://secure.xiaomeihuakefu.cn',
  timeout: 8000,
  retries: 1
};

// 使用原生Node.js模块的HTTP请求函数
function makeHttpRequest(url, data, options = {}) {
  return new Promise((resolve, reject) => {
    const { timeout = SERVER_CONFIG.timeout } = options;
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    // 准备POST数据
    let postData = '';
    if (typeof data === 'object' && data !== null) {
      const params = new URLSearchParams();
      for (const key in data) {
        params.append(key, data[key]);
      }
      postData = params.toString();
    } else {
      postData = data || '';
    }

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'xiaomeihua-app/1.0.0'
      },
      timeout: timeout
    };

    console.log('请求选项:', JSON.stringify(requestOptions, null, 2));
    console.log('请求数据:', postData);

    const req = httpModule.request(requestOptions, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            statusText: res.statusMessage,
            data: parsedData
          });
        } catch (parseError) {
          resolve({
            status: res.statusCode,
            statusText: res.statusMessage,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

// 模拟应用程序的网络请求函数（带重试）
async function makeRequest(url, data, options = {}) {
  const { timeout = SERVER_CONFIG.timeout, retries = SERVER_CONFIG.retries } = options;
  let lastError = null;

  for (let i = 0; i <= retries; i++) {
    try {
      console.log(`尝试请求 ${url} (尝试 ${i+1}/${retries+1})`);

      const response = await makeHttpRequest(url, data, { timeout });
      return response;
    } catch (error) {
      console.error(`请求 ${url} 失败 (尝试 ${i+1}/${retries+1}):`, error.message);
      lastError = error;

      // 如果不是最后一次尝试，等待一段时间后重试
      if (i < retries) {
        const delay = 500;
        console.log(`等待 ${delay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 所有尝试都失败了
  throw lastError;
}

// 模拟卡密验证过程
async function testLicenseVerification(licenseKey) {
  console.log('\n=== 卡密验证测试 ===');
  console.log(`测试卡密: ${licenseKey}`);
  
  if (!licenseKey || licenseKey.trim() === '') {
    console.log('❌ 卡密不能为空');
    return { success: false, message: '卡密不能为空' };
  }

  // 静默处理空格：自动移除空格字符
  licenseKey = licenseKey.replace(/\s/g, '');
  console.log(`处理后的卡密: ${licenseKey}`);
  
  // 尝试主服务器验证
  try {
    const mainUrl = `${SERVER_CONFIG.main}/api/verify.php`;
    const deviceId = generateUUID();
    
    console.log(`\n使用主服务器验证: ${mainUrl}`);
    
    const response = await makeRequest(mainUrl, {
      key: licenseKey,
      device_id: deviceId,
      version: '1.0.0',
      device_name: require('os').hostname() || 'Unknown',
      platform: process.platform || 'unknown',
      check_concurrent_login: 1
    });
    
    if (response.data) {
      console.log('✅ 主服务器响应成功');
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      
      if (response.data.success) {
        return {
          success: true,
          message: '验证成功',
          data: response.data
        };
      } else {
        return {
          success: false,
          message: response.data.message || '验证失败'
        };
      }
    } else {
      console.log('❌ 主服务器响应为空');
    }
    
  } catch (mainError) {
    console.error('❌ 主服务器验证失败:', mainError.message);
    console.error('错误详情:', {
      code: mainError.code,
      errno: mainError.errno,
      syscall: mainError.syscall,
      hostname: mainError.hostname
    });
    
    // 尝试备用服务器验证
    try {
      const backupUrl = `${SERVER_CONFIG.backup}/api/verify.php`;
      const deviceId = generateUUID();
      
      console.log(`\n使用备用服务器验证: ${backupUrl}`);
      
      const response = await makeRequest(backupUrl, {
        key: licenseKey,
        device_id: deviceId,
        version: '1.0.0',
        device_name: require('os').hostname() || 'Unknown',
        platform: process.platform || 'unknown',
        check_concurrent_login: 1
      });
      
      if (response.data) {
        console.log('✅ 备用服务器响应成功');
        console.log('响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success) {
          return {
            success: true,
            message: '验证成功（备用服务器）',
            data: response.data
          };
        } else {
          return {
            success: false,
            message: response.data.message || '验证失败'
          };
        }
      } else {
        console.log('❌ 备用服务器响应为空');
      }
      
    } catch (backupError) {
      console.error('❌ 备用服务器验证失败:', backupError.message);
      console.error('错误详情:', {
        code: backupError.code,
        errno: backupError.errno,
        syscall: backupError.syscall,
        hostname: backupError.hostname
      });
      
      // 分析错误类型并提供具体建议
      const errorAnalysis = analyzeNetworkError(mainError, backupError);
      console.log('\n=== 错误分析 ===');
      console.log(errorAnalysis.analysis);
      console.log('\n=== 解决建议 ===');
      errorAnalysis.suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion}`);
      });
      
      return {
        success: false,
        message: '网络错误，请检查网络连接',
        errorAnalysis
      };
    }
  }
  
  return {
    success: false,
    message: '验证过程中发生未知错误'
  };
}

// 分析网络错误
function analyzeNetworkError(mainError, backupError) {
  const errors = [mainError, backupError].filter(Boolean);
  const errorCodes = errors.map(e => e.code).filter(Boolean);
  const errorMessages = errors.map(e => e.message).filter(Boolean);
  
  let analysis = '';
  let suggestions = [];
  
  // 分析常见错误类型
  if (errorCodes.includes('ENOTFOUND')) {
    analysis = 'DNS解析失败 - 无法找到服务器域名对应的IP地址';
    suggestions = [
      '检查网络连接是否正常',
      '尝试更换DNS服务器（如*******或***************）',
      '检查是否有网络代理或VPN影响DNS解析',
      '联系网络管理员检查DNS设置'
    ];
  } else if (errorCodes.includes('ECONNREFUSED')) {
    analysis = '连接被拒绝 - 服务器拒绝了连接请求';
    suggestions = [
      '检查防火墙是否阻止了应用程序的网络访问',
      '检查杀毒软件是否阻止了网络连接',
      '确认服务器是否正常运行',
      '检查网络端口是否被封锁'
    ];
  } else if (errorCodes.includes('ETIMEDOUT') || errorMessages.some(m => m.includes('timeout'))) {
    analysis = '连接超时 - 网络连接速度过慢或服务器响应超时';
    suggestions = [
      '检查网络连接速度',
      '尝试使用更稳定的网络连接',
      '检查是否有网络拥塞',
      '稍后重试，可能是服务器临时繁忙'
    ];
  } else if (errorCodes.includes('ECONNRESET')) {
    analysis = '连接重置 - 网络连接被意外中断';
    suggestions = [
      '检查网络连接稳定性',
      '重启网络设备（路由器、调制解调器）',
      '检查网络线缆连接',
      '联系网络服务提供商'
    ];
  } else {
    analysis = '未知网络错误 - 可能是多种网络问题的组合';
    suggestions = [
      '检查网络连接是否正常',
      '重启应用程序',
      '重启网络连接',
      '联系技术支持'
    ];
  }
  
  return { analysis, suggestions };
}

// 主函数
async function main() {
  console.log('小梅花AI智能客服 - 登录问题诊断工具');
  console.log('=====================================');
  console.log(`诊断时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`操作系统: ${process.platform} ${process.arch}`);
  
  // 从命令行参数获取卡密，如果没有则使用默认测试卡密
  const licenseKey = process.argv[2] || 'XMHS-E2EFA8698EEC178FBE07';
  
  try {
    const result = await testLicenseVerification(licenseKey);
    
    console.log('\n=== 诊断结果 ===');
    if (result.success) {
      console.log('✅ 登录验证成功！');
      console.log('消息:', result.message);
    } else {
      console.log('❌ 登录验证失败');
      console.log('错误消息:', result.message);
      
      if (result.errorAnalysis) {
        console.log('\n这个错误通常表示网络连接问题。');
        console.log('请按照上面的解决建议进行排查。');
      }
    }
    
  } catch (error) {
    console.error('\n❌ 诊断过程中发生错误:', error.message);
  }
  
  console.log('\n=== 使用说明 ===');
  console.log('如果问题仍然存在，请：');
  console.log('1. 截图保存诊断结果');
  console.log('2. 联系技术支持并提供诊断信息');
  console.log('3. 尝试在不同的网络环境下测试');
}

// 运行诊断
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testLicenseVerification, analyzeNetworkError };
